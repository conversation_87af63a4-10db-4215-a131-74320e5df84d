"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Day1Badge } from "./Day1Badge"
import { ReactionSystem } from "./ReactionSystem"
import { usePathname } from "next/navigation"

interface Comment {
  id: string
  body: string
  created_at: string
  parent_comment_id?: string
  user: {
    id: string
    name: string
    avatar?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  replies?: Comment[]
  depth?: number
  reactions?: Record<string, number>
  userReaction?: string | null
}

interface CompactCommentsSectionProps {
  entryId: string
  canComment: boolean
  userId?: string
  isOpen: boolean
  onToggle: () => void
  commentCount?: number
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Recursive function to build nested comment tree
function buildCommentTree(comments: Comment[]): Comment[] {
  const commentMap = new Map<string, Comment>()
  const rootComments: Comment[] = []

  // First pass: create map of all comments
  comments.forEach(comment => {
    commentMap.set(comment.id, { ...comment, replies: [], depth: 0 })
  })

  // Second pass: build tree structure
  comments.forEach(comment => {
    const commentWithReplies = commentMap.get(comment.id)!

    if (comment.parent_comment_id) {
      const parent = commentMap.get(comment.parent_comment_id)
      if (parent) {
        commentWithReplies.depth = (parent.depth || 0) + 1
        parent.replies!.push(commentWithReplies)
      }
    } else {
      rootComments.push(commentWithReplies)
    }
  })

  return rootComments
}

function updateCommentInTree(comments: Comment[], commentId: string, updates: Partial<Comment>): Comment[] {
  return comments.map(comment => {
    if (comment.id === commentId) {
      return { ...comment, ...updates }
    }
    if (comment.replies && comment.replies.length > 0) {
      return {
        ...comment,
        replies: updateCommentInTree(comment.replies, commentId, updates)
      }
    }
    return comment
  })
}

// Recursive component to render nested comments
function NestedComment({ 
  comment, 
  canComment, 
  userId, 
  onReply, 
  replyingTo, 
  setReplyingTo,
  replyText,
  setReplyText,
  submitting,
  maxDepth = 5
}: {
  comment: Comment
  canComment: boolean
  userId?: string
  onReply: (parentId: string, text: string) => void
  replyingTo: string | null
  setReplyingTo: (id: string | null) => void
  replyText: string
  setReplyText: (text: string) => void
  submitting: boolean
  maxDepth?: number
}) {
  const depth = comment.depth || 0
  const canReply = canComment && userId && depth < maxDepth
  const marginLeft = Math.min(depth * 16, 64) // Max 64px indent

  return (
    <div className="space-y-2">
      <div 
        className="flex items-start space-x-2"
        style={{ marginLeft: `${marginLeft}px` }}
      >
        <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
          {comment.user.avatar ? (
            <img
              src={comment.user.avatar}
              alt={comment.user.name}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <span className="text-xs font-medium text-gray-600">
              {comment.user.name.charAt(0).toUpperCase()}
            </span>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-gray-900 text-xs truncate">
              {comment.user.name}
            </span>
            {comment.user.has_day1_badge && (
              <Day1Badge
                signupNumber={comment.user.signup_number}
                badgeTier={comment.user.badge_tier}
                size="sm"
                className="flex-shrink-0"
              />
            )}
            <span className="text-xs text-gray-500">
              {formatDate(comment.created_at)}
            </span>
          </div>

          <p className="text-gray-700 text-xs leading-relaxed mb-2">
            {comment.body}
          </p>

          {/* Reaction System */}
          <div className="flex items-center gap-3 mb-1">
            <ReactionSystem
              contentId={comment.id}
              contentType="comment"
              currentUserId={userId}
              initialReactions={comment.reactions || {}}
              userReaction={comment.userReaction}
              onReactionUpdate={(reactions, userReaction) => {
                // Update local state for immediate feedback
                setComments(prevComments =>
                  updateCommentInTree(prevComments, comment.id, { reactions, userReaction })
                )
              }}
            />

            {canReply && (
              <button
                onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                className="text-blue-600 hover:text-blue-700 text-xs font-medium"
              >
                {replyingTo === comment.id ? 'Cancel' : 'Reply'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Reply Form */}
      {replyingTo === comment.id && canComment && userId && (
        <div 
          className="bg-blue-50 rounded-lg p-2"
          style={{ marginLeft: `${marginLeft + 32}px` }}
        >
          <form onSubmit={(e) => {
            e.preventDefault()
            if (replyText.trim()) {
              onReply(comment.id, replyText.trim())
            }
          }}>
            <textarea
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              placeholder={`Reply to ${comment.user.name}...`}
              className="w-full p-2 border border-gray-300 rounded text-xs resize-none focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500"
              rows={2}
              disabled={submitting}
              maxLength={1000}
            />
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-gray-500">
                {replyText.length}/1000
              </span>
              <div className="flex gap-1">
                <button
                  type="button"
                  onClick={() => {
                    setReplyingTo(null)
                    setReplyText("")
                  }}
                  className="px-2 py-1 text-gray-600 hover:text-gray-800 text-xs"
                  disabled={submitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!replyText.trim() || submitting}
                  className="px-2 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700 disabled:opacity-50"
                >
                  {submitting ? 'Posting...' : 'Reply'}
                </button>
              </div>
            </div>
          </form>
        </div>
      )}

      {/* Nested Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="space-y-2">
          {comment.replies.map((reply) => (
            <NestedComment
              key={reply.id}
              comment={reply}
              canComment={canComment}
              userId={userId}
              onReply={onReply}
              replyingTo={replyingTo}
              setReplyingTo={setReplyingTo}
              replyText={replyText}
              setReplyText={setReplyText}
              submitting={submitting}
              maxDepth={maxDepth}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function CompactCommentsSection({
  entryId,
  canComment,
  userId,
  isOpen,
  onToggle,
  commentCount = 0
}: CompactCommentsSectionProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(false)
  const [newComment, setNewComment] = useState("")
  const [replyText, setReplyText] = useState("")
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState("")

  const supabase = createSupabaseClient()

  useEffect(() => {
    if (isOpen) {
      loadComments()
    }
  }, [isOpen, entryId])

  const loadComments = async () => {
    if (!isOpen) return
    
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          id,
          body,
          created_at,
          parent_comment_id,
          user:users!user_id (
            id,
            name,
            avatar,
            has_day1_badge,
            signup_number,
            badge_tier
          )
        `)
        .eq('diary_entry_id', entryId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Error loading comments:', error)
      } else {
        // Load reaction data for each comment
        const commentsWithReactions = await Promise.all(
          (data || []).map(async (comment) => {
            try {
              const response = await fetch(`/api/comments/${comment.id}/reactions`)
              if (response.ok) {
                const reactionData = await response.json()
                return {
                  ...comment,
                  reactions: reactionData.reactions || {},
                  userReaction: reactionData.userReaction
                }
              }
            } catch (err) {
              console.error('Error loading reactions for comment:', comment.id, err)
            }
            return {
              ...comment,
              reactions: {},
              userReaction: null
            }
          })
        )
        const nestedComments = buildCommentTree(commentsWithReactions)
        setComments(nestedComments)
      }
    } catch (err) {
      console.error('Error loading comments:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newComment.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          diary_entry_id: entryId,
          user_id: userId,
          body: newComment.trim()
        })

      if (error) {
        setError(`Failed to post comment: ${error.message}`)
      } else {
        setNewComment("")
        loadComments()
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  const handleSubmitReply = async (parentCommentId: string, text: string) => {
    if (!text.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          diary_entry_id: entryId,
          user_id: userId,
          body: text,
          parent_comment_id: parentCommentId
        })

      if (error) {
        setError(`Failed to post reply: ${error.message}`)
      } else {
        setReplyText("")
        setReplyingTo(null)
        loadComments()
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="border-t border-gray-100">
      {/* Comment Toggle Button */}
      <button
        onClick={onToggle}
        className="w-full px-4 py-2 text-left text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-colors flex items-center justify-between"
      >
        <span className="flex items-center gap-2">
          💬 {commentCount > 0 ? `${commentCount} comments` : 'Comments'}
        </span>
        <span className="text-xs">
          {isOpen ? '▲' : '▼'}
        </span>
      </button>

      {/* Expanded Comments Section */}
      {isOpen && (
        <div className="px-4 pb-4 bg-gray-50">
          {/* Comment Form */}
          {canComment && userId ? (
            <form onSubmit={handleSubmitComment} className="mb-4">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Share your thoughts..."
                className="w-full p-2 border border-gray-300 rounded text-sm resize-none focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500"
                rows={2}
                maxLength={1000}
              />
              
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-500">
                  {newComment.length}/1000 characters
                </span>
                
                {error && (
                  <span className="text-red-600 text-xs">{error}</span>
                )}
                
                <button
                  type="submit"
                  disabled={submitting || !newComment.trim()}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {submitting ? "Posting..." : "Post"}
                </button>
              </div>
            </form>
          ) : !canComment ? (
            <div className="mb-4 p-3 bg-yellow-50 rounded text-xs text-gray-600">
              Subscribe to join the conversation
            </div>
          ) : (
            <div className="mb-4 p-3 bg-yellow-50 rounded text-xs text-gray-600">
              Sign in to comment
            </div>
          )}

          {/* Comments List */}
          {loading ? (
            <div className="text-center py-4">
              <div className="text-xs text-gray-500">Loading comments...</div>
            </div>
          ) : comments.length > 0 ? (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {comments.map((comment) => (
                <NestedComment
                  key={comment.id}
                  comment={comment}
                  canComment={canComment}
                  userId={userId}
                  onReply={handleSubmitReply}
                  replyingTo={replyingTo}
                  setReplyingTo={setReplyingTo}
                  replyText={replyText}
                  setReplyText={setReplyText}
                  submitting={submitting}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 text-xs">
                No comments yet. Be the first to share your thoughts!
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
