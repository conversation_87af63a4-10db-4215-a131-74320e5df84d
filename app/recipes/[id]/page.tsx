import { createSupabaseServerClient } from "@/lib/supabase/client"
import Image from "next/image"
import CookedItUploader from "@/components/CookedItUploader"

export default async function RecipePage({ params }: { params: { id: string } }) {
  const supabase = await createSupabaseServerClient()

  const { data: recipe } = await supabase
    .from('recipes' as any)
    .select('id, user_id, title, description, is_free, price_cents, cover_photo_url, cooked_count, created_at, users!inner(name, profile_picture_url)')
    .eq('id', params.id)
    .single()

  if (!recipe) {
    return <div className="min-h-screen flex items-center justify-center"><div className="text-gray-600 font-serif">Recipe not found</div></div>
  }

  const { data: photos } = await supabase
    .from('recipe_photos' as any)
    .select('id, url, alt_text')
    .eq('recipe_id', params.id)
    .order('created_at', { ascending: true })

  const { data: videos } = await supabase
    .from('recipe_videos' as any)
    .select('id, r2_public_url, title')
    .eq('recipe_id', params.id)
    .order('created_at', { ascending: false })

  const { data: ingredients } = await supabase
    .from('recipe_ingredients' as any)
    .select('position, section, amount, unit, item, note')
    .eq('recipe_id', params.id)
    .order('position', { ascending: true })

  const { data: steps } = await supabase
    .from('recipe_steps' as any)
    .select('position, instruction, time_hint')
    .eq('recipe_id', params.id)
    .order('position', { ascending: true })

  const { data: cookEvents } = await supabase
    .from('recipe_cook_events' as any)
    .select('id, user_id, created_at')
    .eq('recipe_id', params.id)
    .order('created_at', { ascending: false })
    .limit(10)

  return (
    <div className="min-h-screen bg-white py-8">
      <div className="max-w-3xl mx-auto px-4">
        <h1 className="text-4xl font-serif text-gray-900 mb-2">{recipe.title}</h1>
        <p className="text-gray-600 mb-4">By {recipe.users?.name} • {new Date(recipe.created_at).toLocaleDateString()}</p>
        <div className="flex items-center gap-3 mb-6">
          <CookedItUploader recipeId={recipe.id} />
          <span className="text-sm text-gray-700">COOKED IT ({recipe.cooked_count || 0})</span>
        </div>

        {recipe.description && (
          <div className="prose max-w-none text-gray-800 mb-6 whitespace-pre-wrap">{recipe.description}</div>
        )}

        {photos && photos.length > 0 && (
          <div className="grid grid-cols-2 gap-3 mb-6">
            {photos.map((p) => (
              <div key={p.id} className="relative border border-gray-200 rounded-lg overflow-hidden flex justify-center">
                <Image src={p.url} alt={p.alt_text} width={600} height={450} className="object-contain bg-gray-50" />
              </div>
            ))}
          </div>
        )}

        {videos && videos.length > 0 && (
          <div className="space-y-3 mb-6">
            {videos.map((v) => (
              <video key={v.id} controls className="w-full rounded-lg border border-gray-200">
                <source src={v.r2_public_url} />
              </video>
            ))}
          </div>
        )}

        {/* Ingredients */}
        {ingredients && ingredients.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-serif text-gray-900 mb-3">Ingredients</h2>
            {/* Group by section */}
            {(() => {
              const bySection = ingredients.reduce((acc: Record<string, any[]>, it: any) => {
                const key = it.section || ' '
                acc[key] = acc[key] || []
                acc[key].push(it)
                return acc
              }, {})
              return (
                <div className="space-y-4">
                  {Object.entries(bySection).map(([section, list]) => (
                    <div key={section}>
                      {section.trim() !== '' && (
                        <h3 className="font-medium text-gray-800 mb-2">{section}</h3>
                      )}
                      <ul className="list-disc pl-6 space-y-1">
                        {list.map((it: any, idx: number) => (
                          <li key={idx} className="text-gray-800">
                            <span className="font-serif">
                              {it.amount ? `${it.amount} ` : ''}
                              {it.unit ? `${it.unit} ` : ''}
                              {it.item}
                            </span>
                            {it.note ? <span className="text-gray-500"> — {it.note}</span> : null}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              )
            })()}
          </div>
        )}

        {/* Instructions */}
        {steps && steps.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-serif text-gray-900 mb-3">Instructions</h2>
            <ol className="list-decimal pl-6 space-y-3">
              {steps.map((st: any, i: number) => (
                <li key={i} className="text-gray-800">
                  <div className="whitespace-pre-wrap font-serif">{st.instruction}</div>
                  {st.time_hint && (
                    <div className="text-sm text-gray-500 mt-1 italic">{st.time_hint}</div>
                  )}
                </li>
              ))}
            </ol>
          </div>
        )}

        <h2 className="text-2xl font-serif text-gray-900 mb-3">COOKED IT Stories</h2>
        {cookEvents && cookEvents.length > 0 ? (
          <div className="space-y-3">
            {cookEvents.map((e) => (
              <div key={e.id} className="border border-gray-200 rounded-lg p-3 text-sm text-gray-700">{new Date(e.created_at).toLocaleString()}</div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No cook stories yet. Be the first!</p>
        )}
      </div>
    </div>
  )
}

