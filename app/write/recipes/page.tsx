"use client"

import { useEffect, useState, useRef } from "react"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import RecipePhotoUpload from "@/components/RecipePhotoUpload"
import RecipeVideoUpload from "@/components/RecipeVideoUpload"

import dynamic from "next/dynamic"
const IngredientsEditor = dynamic(() => import('@/components/recipes/IngredientsEditor'), { ssr: false })
const StepsEditor = dynamic(() => import('@/components/recipes/StepsEditor'), { ssr: false })
export default function RecipeCreate() {
  const supabase = createSupabaseClient()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [recipeId, setRecipeId] = useState<string | null>(null)
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [isFree, setIsFree] = useState(true)
  const [priceCents, setPriceCents] = useState<number | null>(null)
  const [ingredients, setIngredients] = useState<any[]>([])
  const [steps, setSteps] = useState<any[]>([])

  const titleRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    (async () => {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      if (error || !authUser) {
        window.location.href = '/login'
        return
      }
      setUser(authUser)
      setLoading(false)
    })()
  }, [supabase])

  const ensureRecipeId = async (): Promise<string | null> => {
    if (recipeId) return recipeId
    const { data, error } = await supabase
      .from('recipes' as any)
      .insert({ user_id: user.id, title: title || 'Untitled Recipe', description: description || '', is_free: isFree, price_cents: priceCents, is_hidden: true })
      .select('id')
      .single()
    if (error) { setError(error.message); return null }
    setRecipeId(data.id)
    return data.id
  }

  const handlePublish = async () => {
    try {
      setSaving(true)
      setError(null)
      const rid = await ensureRecipeId()
      if (!rid) throw new Error('Failed to save recipe')

      // Update recipe basics and unhide
      const { error: updErr } = await supabase
        .from('recipes' as any)
        .update({ title, description, is_free: isFree, price_cents: priceCents, is_hidden: false })
        .eq('id', rid)
      if (updErr) throw updErr

      // Persist ingredients
      await supabase.from('recipe_ingredients' as any).delete().eq('recipe_id', rid)
      if (ingredients.length > 0) {
        const rows = ingredients.map((ing, idx) => ({
          recipe_id: rid,
          position: idx + 1,
          section: ing.section || null,
          amount: ing.amount || null,
          unit: ing.unit || null,
          item: ing.item,
          note: ing.note || null
        }))
        const { error: ingErr } = await supabase.from('recipe_ingredients' as any).insert(rows)
        if (ingErr) throw ingErr
      }

      // Persist steps
      await supabase.from('recipe_steps' as any).delete().eq('recipe_id', rid)
      if (steps.length > 0) {
        const rows = steps.map((st, idx) => ({
          recipe_id: rid,
          position: idx + 1,
          instruction: st.instruction,
          time_hint: st.time_hint || null
        }))
        const { error: stepErr } = await supabase.from('recipe_steps' as any).insert(rows)
        if (stepErr) throw stepErr
      }

      window.location.href = `/recipes/${rid}`
    } catch (e: any) {
      setError(e.message || 'Publish failed')
    } finally {
      setSaving(false)
    }
  }

  if (loading) return <div className="min-h-screen flex items-center justify-center"><div className="font-serif text-gray-600">Loading...</div></div>
  if (!user) return null

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="mb-6 flex items-center justify-between">
          <Link href="/write" className="text-sm text-gray-500 hover:text-gray-700">← Back</Link>
          <div className="flex gap-2">
            <button onClick={handlePublish} disabled={saving} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50">{saving ? 'Publishing…' : 'Publish'}</button>
          </div>
        </div>

        <h1 className="text-4xl font-serif text-gray-900 mb-6">Create a Recipe</h1>

        {error && <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-600 text-sm">{error}</div>}

        {/* Basics */}
        <div className="space-y-4 mb-6">
          <input
            ref={titleRef}
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Recipe title"
            className="w-full border border-gray-300 rounded-lg px-4 py-3 font-serif text-lg"
          />
          <p className="text-xs text-gray-500 italic">e.g., The Best Banana Bread</p>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe your recipe…"
            rows={5}
            className="w-full border border-gray-300 rounded-lg px-4 py-3 font-serif"
          />
          <p className="text-xs text-gray-500 italic">e.g., Share what makes your recipe special, substitutions, tips.</p>
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input type="checkbox" checked={isFree} onChange={(e) => setIsFree(e.target.checked)} />
              <span className="font-serif text-gray-700">Free recipe</span>
            </label>
            {!isFree && (
              <input
                type="number"
                min={0}
                placeholder="Price (USD cents)"
                value={priceCents ?? ''}
                onChange={(e) => setPriceCents(e.target.value === '' ? null : Math.max(0, Math.floor(Number(e.target.value))))}
                className="border border-gray-300 rounded px-3 py-2 w-48"
              />
            )}
          </div>
        </div>

        {/* Media */}
        <RecipePhotoUpload recipeId={recipeId} onEnsureRecipeId={ensureRecipeId} />
        <RecipeVideoUpload recipeId={recipeId} onEnsureRecipeId={ensureRecipeId} />

        {/* Ingredients and Steps */}
        {/* In this initial pass, we keep these editors local-only. In the next increment we will persist to recipe_ingredients/recipe_steps. */}
        {/* @ts-expect-error: dynamic import will handle client-only components */}
        <IngredientsEditor value={ingredients} onChange={setIngredients} />
        {/* @ts-expect-error */}
        <StepsEditor value={steps} onChange={setSteps} />
      </div>
    </div>
  )
}

